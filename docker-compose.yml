version: "3.9"

services:
  traefik:
    image: traefik:v3.0
    container_name: traefik
    command:
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      # Let's Encrypt
      - --certificatesresolvers.le.acme.email=${LE_EMAIL}
      - --certificatesresolvers.le.acme.storage=/letsencrypt/acme.json
      - --certificatesresolvers.le.acme.httpchallenge=true
      - --certificatesresolvers.le.acme.httpchallenge.entrypoint=web
      # Logging
      - --accesslog=true
      - --accesslog.filepath=/var/log/traefik/access.log
      - --log.level=INFO
      - --log.filepath=/var/log/traefik/traefik.log
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./letsencrypt:/letsencrypt
      - ./traefik/logs:/var/log/traefik
    restart: unless-stopped
    labels:
      - traefik.enable=true

  authelia:
    image: authelia/authelia:latest
    container_name: authelia
    environment:
      - TZ=Europe/Amsterdam
    volumes:
      - ./authelia:/config
    restart: unless-stopped
    labels:
      - traefik.enable=true
      - traefik.http.routers.authelia.rule=Host(`${AUTH_DOMAIN}`)
      - traefik.http.routers.authelia.entrypoints=websecure
      - traefik.http.routers.authelia.tls.certresolver=le
      - traefik.http.services.authelia.loadbalancer.server.port=9091
      # ForwardAuth middleware (MFA/2FA)
      - traefik.http.middlewares.authelia.forwardauth.address=http://authelia:9091/api/verify?rd=https://${AUTH_DOMAIN}
      - traefik.http.middlewares.authelia.forwardauth.trustForwardHeader=true
      - traefik.http.middlewares.authelia.forwardauth.authResponseHeaders=Remote-User,Remote-Groups,Remote-Name,Remote-Email

  langflow:
    image: langflowai/langflow:latest
    container_name: langflow
    environment:
      - HOST=0.0.0.0
      - PORT=7860
      - PYTHONUNBUFFERED=1
      - LOG_LEVEL=INFO
    volumes:
      - ./langflow/data:/root/.langflow
      - ./langflow/logs:/app/logs
    restart: unless-stopped
    labels:
      - traefik.enable=true
      - traefik.http.routers.langflow.rule=Host(`${DOMAIN}`)
      - traefik.http.routers.langflow.entrypoints=websecure
      - traefik.http.routers.langflow.tls.certresolver=le
      - traefik.http.services.langflow.loadbalancer.server.port=7860
      - traefik.http.routers.langflow.middlewares=authelia@docker

networks:
  default:
    name: edge
