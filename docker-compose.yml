version: "3.9"

services:
  traefik:
    image: traefik:v3.0
    container_name: traefik
    command:
      - --providers.docker=true
      - --providers.docker.exposedbydefault=false
      - --entrypoints.web.address=:80
      - --entrypoints.websecure.address=:443
      # Let's Encrypt configuration for basiraone.nl
      - --certificatesresolvers.le.acme.email=${LE_EMAIL}
      - --certificatesresolvers.le.acme.storage=/letsencrypt/acme.json
      - --certificatesresolvers.le.acme.httpchallenge=true
      - --certificatesresolvers.le.acme.httpchallenge.entrypoint=web
      # Security headers
      - --entrypoints.websecure.http.middlewares=security-headers@docker
      # Logging
      - --accesslog=true
      - --accesslog.filepath=/var/log/traefik/access.log
      - --log.level=INFO
      - --log.filepath=/var/log/traefik/traefik.log
      # API and dashboard (disabled for production)
      - --api.dashboard=false
      - --api.insecure=false
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
      - ./letsencrypt:/letsencrypt
      - ./traefik/logs:/var/log/traefik
    restart: unless-stopped
    environment:
      - TZ=Europe/Amsterdam
    labels:
      - traefik.enable=true
      # Security headers middleware
      - traefik.http.middlewares.security-headers.headers.customRequestHeaders.X-Forwarded-Proto=https
      - traefik.http.middlewares.security-headers.headers.customResponseHeaders.X-Frame-Options=DENY
      - traefik.http.middlewares.security-headers.headers.customResponseHeaders.X-Content-Type-Options=nosniff
      - traefik.http.middlewares.security-headers.headers.customResponseHeaders.Referrer-Policy=strict-origin-when-cross-origin
      - traefik.http.middlewares.security-headers.headers.customResponseHeaders.Strict-Transport-Security=max-age=31536000; includeSubDomains
      # HTTP to HTTPS redirect
      - traefik.http.routers.http-catchall.rule=hostregexp(`{host:.+}`)
      - traefik.http.routers.http-catchall.entrypoints=web
      - traefik.http.routers.http-catchall.middlewares=redirect-to-https
      - traefik.http.middlewares.redirect-to-https.redirectscheme.scheme=https

  authelia:
    image: authelia/authelia:latest
    container_name: authelia
    environment:
      - TZ=Europe/Amsterdam
      - AUTHELIA_LOG_LEVEL=info
      - AUTHELIA_LOG_FORMAT=text
    volumes:
      - ./authelia:/config
    restart: unless-stopped
    depends_on:
      - traefik
    healthcheck:
      test:
        [
          "CMD",
          "wget",
          "--no-verbose",
          "--tries=1",
          "--spider",
          "http://localhost:9091/api/health",
        ]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - traefik.enable=true
      - traefik.http.routers.authelia.rule=Host(`${AUTH_DOMAIN}`)
      - traefik.http.routers.authelia.entrypoints=websecure
      - traefik.http.routers.authelia.tls.certresolver=le
      - traefik.http.routers.authelia.middlewares=security-headers@docker
      - traefik.http.services.authelia.loadbalancer.server.port=9091
      # ForwardAuth middleware (MFA/2FA)
      - traefik.http.middlewares.authelia.forwardauth.address=http://authelia:9091/api/verify?rd=https://${AUTH_DOMAIN}
      - traefik.http.middlewares.authelia.forwardauth.trustForwardHeader=true
      - traefik.http.middlewares.authelia.forwardauth.authResponseHeaders=Remote-User,Remote-Groups,Remote-Name,Remote-Email

  langflow:
    image: langflowai/langflow:latest
    container_name: langflow
    environment:
      - HOST=0.0.0.0
      - PORT=7860
      - PYTHONUNBUFFERED=1
      - LOG_LEVEL=INFO
      - TZ=Europe/Amsterdam
      - LANGFLOW_DATABASE_URL=sqlite:///root/.langflow/langflow.db
      - LANGFLOW_CACHE_TYPE=memory
      - LANGFLOW_SUPERUSER=<EMAIL>
      - LANGFLOW_SUPERUSER_PASSWORD=WJ8sDACH%t^LY#^idSj&c5diOaC4qCc9!
    volumes:
      - ./langflow/data:/root/.langflow
      - ./langflow/logs:/app/logs
    restart: unless-stopped
    depends_on:
      - authelia
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7860/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    labels:
      - traefik.enable=true
      - traefik.http.routers.langflow.rule=Host(`${DOMAIN}`)
      - traefik.http.routers.langflow.entrypoints=websecure
      - traefik.http.routers.langflow.tls.certresolver=le
      - traefik.http.routers.langflow.middlewares=authelia@docker,security-headers@docker
      - traefik.http.services.langflow.loadbalancer.server.port=7860

networks:
  default:
    name: basiraone-edge
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  letsencrypt:
    driver: local
  authelia-config:
    driver: local
  langflow-data:
    driver: local
  traefik-logs:
    driver: local
