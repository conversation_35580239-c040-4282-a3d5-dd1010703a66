# LangFlow + Traefik + Authelia (MFA) — Productieklare Stack

Deze stack zet **LangFlow** veilig achter **Traefik** met **Authelia** voor login + **MFA (TOTP)**.
<PERSON><PERSON><PERSON>, self-hosted, en met logs zodat je problemen van gebruikers kunt debuggen.

## Inhoud
- `docker-compose.yml` — complete stack
- `.env` — domeinen & Let's Encrypt e-mail
- `authelia/configuration.yml` — Authelia-config (MFA verplicht)
- `authelia/users_database.yml` — gebruikers (met argon2 hash)
- `letsencrypt/acme.json` — opslag voor Let's Encrypt (wordt gevuld)
- `langflow/data/` — LangFlow data (persistent)
- `langflow/logs/` — extra logmap (optioneel)
- `traefik/logs/` — Traefik access & service logs
- `scripts/` — hulpscripts (hash genereren, starten/stoppen)

## Voorwaarden
- **Docker** en **<PERSON>er Compose** (Ubuntu 22.04/24.04).
- Twee DNS A-records naar jouw server-IP:
  - `ai.jouwdomein.nl`
  - `auth.jouwdomein.nl`

## 1) `.env` aanpassen
Pas in `.env` deze waarden aan:
```env
DOMAIN=ai.jouwdomein.nl
AUTH_DOMAIN=auth.jouwdomein.nl
LE_EMAIL=<EMAIL>
```

## 2) Authelia configureren
- Open `authelia/configuration.yml` en **vervang alle 'REPLACE_WITH_LONG_RANDOM_*'** door lange, random strings.
- Vervang **'jouwdomein.nl'** (basisdomein) door je eigen basisdomein (bv. `example.nl`).
- In de `access_control.rules` kun je toegestane users/groups beheren.

## 3) Admin-wachtwoord hash genereren
Voer uit:
```bash
./scripts/generate_authelia_hash.sh
```
Kopieer de argon2-hash naar `authelia/users_database.yml` bij `password:`.

## 4) Starten
```bash
./scripts/up.sh
```
Dit maakt (zo nodig) `letsencrypt/acme.json` aan, zet permissies en start de stack.

- Authelia-portaal: `https://auth.<jouwdomein>`
- LangFlow (achter MFA): `https://ai.<jouwdomein>`

Eerste login: voer e-mail + wachtwoord in en **registreer TOTP** (scan QR-code in Google/Microsoft Authenticator).
Daarna is elke login **wachtwoord + 6-cijferige code**.

## 5) Logs bekijken

**LangFlow (app-logs)**
```bash
docker logs -f langflow
```

**Traefik (proxy & toegang)**
```bash
tail -f traefik/logs/access.log
tail -f traefik/logs/traefik.log
```

**Authelia (auth/MFA)**
```bash
docker logs -f authelia
tail -f authelia/notification.txt
```

> Tip: Combineer Traefik **access.log** (wie/wanneer) met **Authelia logs** (auth outcome) en
> `docker logs langflow` (app-fouten) om problemen van gebruikers snel te herleiden.

## 6) Stoppen / verwijderen
```bash
./scripts/down.sh
```

## Veelvoorkomende problemen
- **Let's Encrypt faalt**: controleer DNS-records, poorten 80/443 open, firewall/NAT.
- **Authelia redirect loop**: `session.domain` moet het **basisdomein** zijn (bijv. `example.nl`, niet `auth.example.nl`).
- **Geen toegang na login**: staat je gebruiker in `group: admins` of expliciet `user:` in de regel?
- **MFA verplicht**: staat `policy: two_factor` aan op het app-domein? (is hier standaard zo).

## Uitbreiden
- Rate limiting / bruteforce: Traefik middleware of fail2ban toevoegen.
- SMTP in Authelia voor mailnotificaties.
- Backups van `langflow/data` (rsync/cron).
