# Production Environment Configuration for basiraone.nl
# Copy this to .env and customize as needed

# Domain Configuration
DOMAIN=ai.basiraone.nl
AUTH_DOMAIN=auth.basiraone.nl

# Let's Encrypt Configuration
LE_EMAIL=<EMAIL>

# Timezone
TZ=Europe/Amsterdam

# Authelia Configuration
AUTHELIA_LOG_LEVEL=info
AUTHELIA_LOG_FORMAT=text

# LangFlow Configuration
LANGFLOW_SUPERUSER=<EMAIL>
LANGFLOW_SUPERUSER_PASSWORD=WJ8sDACH%t^LY#^idSj&c5diOaC4qCc9!
LANGFLOW_DATABASE_URL=sqlite:///root/.langflow/langflow.db
LANGFLOW_CACHE_TYPE=memory

# Security Settings
AUTHELIA_SESSION_EXPIRATION=3600
AUTHELIA_INACTIVITY_TIMEOUT=300

# Optional: SMTP Configuration for Authelia (uncomment and configure)
# AUTHELIA_NOTIFIER_SMTP_HOST=smtp.gmail.com
# AUTHELIA_NOTIFIER_SMTP_PORT=587
# AUTHELIA_NOTIFIER_SMTP_USERNAME=<EMAIL>
# AUTHELIA_NOTIFIER_SMTP_PASSWORD=your-app-password
# AUTHELIA_NOTIFIER_SMTP_SENDER=<EMAIL>

# Optional: Database Configuration (for production scale)
# AUTHELIA_STORAGE_MYSQL_HOST=mysql
# AUTHELIA_STORAGE_MYSQL_PORT=3306
# AUTHELIA_STORAGE_MYSQL_DATABASE=authelia
# AUTHELIA_STORAGE_MYSQL_USERNAME=authelia
# AUTHELIA_STORAGE_MYSQL_PASSWORD=secure_password

# Optional: Redis Configuration (for session storage)
# AUTHELIA_SESSION_REDIS_HOST=redis
# AUTHELIA_SESSION_REDIS_PORT=6379
# AUTHELIA_SESSION_REDIS_PASSWORD=redis_password

# Backup Configuration
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
