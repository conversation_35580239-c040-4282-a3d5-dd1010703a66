# Authelia minimale configuratie met MFA (TOTP) ingeschakeld.
# BELANGRIJK: vervang 'jouwdomein.nl' overal door je EIGEN basisdomein (zonder subdomein).
# Voorbeeld: als DOMAIN=ai.example.nl en AUTH_DOMAIN=auth.example.nl dan is je basisdomein 'example.nl'.
server:
  address: "0.0.0.0"
  port: 9091

# Gebruik sterke geheimen; je kunt deze placeholders laten staan voor test,
# maar VERVANG ZE voor productie.
jwt_secret: "h1dp7kbnxC+4kYFQ2456GgM93tM6cxzYbW8wJd0eVsH9uPo1K7r+X3pFjmNlQv0e"

log:
  level: info

theme: auto

session:
  name: authelia_session
  secret: "REPLACE_WITH_LONG_RANDOM_SESSION_SECRET"
  expiration: 3600
  inactivity: 300
  domain: "jouwdomein.nl"  # BASISDOMEIN, geen subdomein

storage:
  encryption_key: "REPLACE_WITH_LONG_RANDOM_STORAGE_KEY"
  local:
    path: /config/db.sqlite3

notifier:
  filesystem:
    filename: /config/notification.txt
  # Voor productie: stel SMTP in om e-mails te versturen.

authentication_backend:
  file:
    path: /config/users_database.yml
    password:
      algorithm: argon2
      iterations: 3
      memory: 65536
      parallelism: 4
      salt_length: 16
      key_length: 32

access_control:
  default_policy: deny
  rules:
    - domain: "ai.jouwdomein.nl"
      policy: two_factor   # login + MFA verplicht
      subject:
        - "group:admins"
        - "user:<EMAIL>"
    - domain: "auth.jouwdomein.nl"
      policy: bypass       # portaal zelf mag zonder MFA geladen worden

# MFA: TOTP (Google/Microsoft Authenticator) en optioneel WebAuthn (FIDO2)
totp:
  issuer: "jouwdomein.nl"

webauthn:
  disable: false

identity_providers:
  oidc: {}  # optioneel uit te breiden
