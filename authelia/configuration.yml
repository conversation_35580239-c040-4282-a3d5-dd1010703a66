# Authelia production configuration with <PERSON><PERSON> (TOTP) enabled for basiraone.nl
server:
  address: "0.0.0.0"
  port: 9091

# Production-ready JWT secret (256-bit)
jwt_secret: "h1dp7kbnxC+4kYFQ2456GgM93tM6cxzYbW8wJd0eVsH9uPo1K7r+X3pFjmNlQv0e"

log:
  level: info

theme: auto

session:
  name: authelia_session
  secret: "K9mP2vX8qR5tY7uI3oP6sA1dF4gH7jK0lZ3xC6vB9nM2qW5eR8tY1uI4oP7sA0dF"
  expiration: 3600
  inactivity: 300
  domain: "basiraone.nl" # Base domain for session cookies

storage:
  encryption_key: "B7nM4qW9eR2tY5uI8oP1sA4dF7gH0jK3lZ6xC9vB2nM5qW8eR1tY4uI7oP0sA3dF"
  local:
    path: /config/db.sqlite3

notifier:
  filesystem:
    filename: /config/notification.txt
  # Production SMTP configuration can be added here when needed

authentication_backend:
  file:
    path: /config/users_database.yml
    password:
      algorithm: argon2
      iterations: 3
      memory: 65536
      parallelism: 4
      salt_length: 16
      key_length: 32

access_control:
  default_policy: deny
  rules:
    - domain: "ai.basiraone.nl"
      policy: two_factor # Requires login + MFA
      subject:
        - "group:admins"
        - "user:<EMAIL>"
    - domain: "auth.basiraone.nl"
      policy: bypass # Auth portal itself can be loaded without MFA

# MFA: TOTP (Google/Microsoft Authenticator) and optional WebAuthn (FIDO2)
totp:
  issuer: "basiraone.nl"

webauthn:
  disable: false

identity_providers:
  oidc: {} # optioneel uit te breiden
